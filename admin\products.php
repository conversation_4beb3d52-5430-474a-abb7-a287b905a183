<?php
/**
 * Admin Product Management Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/Validator.php';
require_once '../includes/FileUpload.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

$db = Database::getInstance();

// Handle product actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'add_product') {
            $validator = new Validator($_POST);
            $validator->required('product_code', 'Product code is required')
                     ->required('name', 'Product name is required')
                     ->required('price', 'Price is required')
                     ->required('pv_value', 'PV value is required')
                     ->numeric('price', 'Price must be a number')
                     ->numeric('pv_value', 'PV value must be a number');
            
            if ($validator->passes()) {
                $productCode = sanitizeInput($_POST['product_code']);
                $name = sanitizeInput($_POST['name']);
                $description = sanitizeInput($_POST['description'] ?? '');
                $price = (float) $_POST['price'];
                $pvValue = (float) $_POST['pv_value'];
                $status = sanitizeInput($_POST['status'] ?? 'active');

                // Check if product code already exists
                $checkStmt = $db->prepare("SELECT id FROM products WHERE product_code = ?");
                $checkStmt->execute([$productCode]);
                if ($checkStmt->fetch()) {
                    throw new Exception("Product code already exists");
                }

                // Handle image upload
                $imagePath = null;
                if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
                    $fileUpload = new FileUpload();
                    $uploadResult = $fileUpload->uploadProductImage($_FILES['product_image'], $productCode);

                    if ($uploadResult['success']) {
                        $imagePath = $uploadResult['filename'];
                    } else {
                        throw new Exception("Image upload failed: " . $uploadResult['message']);
                    }
                }

                $stmt = $db->prepare("INSERT INTO products (product_code, name, description, image, price, pv_value, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$productCode, $name, $description, $imagePath, $price, $pvValue, $status, $adminId]);

                $success = "Product added successfully!";
            } else {
                $error = implode('<br>', $validator->errors());
            }
            
        } elseif ($action === 'edit_product') {
            $validator = new Validator($_POST);
            $validator->required('product_id', 'Product ID is required')
                     ->required('product_code', 'Product code is required')
                     ->required('name', 'Product name is required')
                     ->required('price', 'Price is required')
                     ->required('pv_value', 'PV value is required')
                     ->numeric('price', 'Price must be a number')
                     ->numeric('pv_value', 'PV value must be a number');
            
            if ($validator->passes()) {
                $productId = (int) $_POST['product_id'];
                $productCode = sanitizeInput($_POST['product_code']);
                $name = sanitizeInput($_POST['name']);
                $description = sanitizeInput($_POST['description'] ?? '');
                $price = (float) $_POST['price'];
                $pvValue = (float) $_POST['pv_value'];
                $status = sanitizeInput($_POST['status'] ?? 'active');
                
                // Check if product code already exists for other products
                $checkStmt = $db->prepare("SELECT id FROM products WHERE product_code = ? AND id != ?");
                $checkStmt->execute([$productCode, $productId]);
                if ($checkStmt->fetch()) {
                    throw new Exception("Product code already exists");
                }

                // Get current product data for image handling
                $currentProductStmt = $db->prepare("SELECT image FROM products WHERE id = ?");
                $currentProductStmt->execute([$productId]);
                $currentProduct = $currentProductStmt->fetch();

                $imagePath = $currentProduct['image']; // Keep existing image by default

                // Handle image upload
                if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] === UPLOAD_ERR_OK) {
                    $fileUpload = new FileUpload();
                    $uploadResult = $fileUpload->uploadProductImage($_FILES['product_image'], $productCode);

                    if ($uploadResult['success']) {
                        // Delete old image if exists
                        if ($currentProduct['image']) {
                            $fileUpload->deleteFile($currentProduct['image']);
                        }
                        $imagePath = $uploadResult['filename'];
                    } else {
                        throw new Exception("Image upload failed: " . $uploadResult['message']);
                    }
                }

                $stmt = $db->prepare("UPDATE products SET product_code = ?, name = ?, description = ?, image = ?, price = ?, pv_value = ?, status = ? WHERE id = ?");
                $stmt->execute([$productCode, $name, $description, $imagePath, $price, $pvValue, $status, $productId]);

                $success = "Product updated successfully!";
            } else {
                $error = implode('<br>', $validator->errors());
            }
            
        } elseif ($action === 'delete_product') {
            $productId = (int) $_POST['product_id'];

            // Check if product is used in any transactions
            $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM pv_transactions WHERE product_id = ?");
            $checkStmt->execute([$productId]);
            $usage = $checkStmt->fetch();

            if ($usage['count'] > 0) {
                throw new Exception("Cannot delete product as it has been used in transactions. You can deactivate it instead.");
            }

            // Get product image before deletion
            $productStmt = $db->prepare("SELECT image FROM products WHERE id = ?");
            $productStmt->execute([$productId]);
            $product = $productStmt->fetch();

            // Delete the product
            $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$productId]);

            // Delete associated image file
            if ($product && $product['image']) {
                $fileUpload = new FileUpload();
                $fileUpload->deleteFile($product['image']);
            }

            $success = "Product deleted successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query conditions
$whereConditions = [];
$params = [];

if ($status && $status !== 'all') {
    $whereConditions[] = "status = ?";
    $params[] = $status;
}

if ($search) {
    $whereConditions[] = "(product_code LIKE ? OR name LIKE ? OR description LIKE ?)";
    $searchTerm = '%' . $search . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get products
$productsStmt = $db->prepare("
    SELECT p.*, a.full_name as created_by_name,
           (SELECT COUNT(*) FROM pv_transactions WHERE product_id = p.id) as usage_count
    FROM products p
    LEFT JOIN admin a ON p.created_by = a.id
    {$whereClause}
    ORDER BY p.created_at DESC
");
$productsStmt->execute($params);
$products = $productsStmt->fetchAll();

// Get statistics
$statsStmt = $db->query("
    SELECT 
        COUNT(*) as total_products,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_products,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_products,
        AVG(price) as avg_price,
        AVG(pv_value) as avg_pv
    FROM products
");
$stats = $statsStmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Management - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i><?php echo SITE_NAME; ?> Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="franchises.php">
                            <i class="fas fa-store me-1"></i>Franchises
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="product-approvals.php">
                            <i class="fas fa-check-circle me-1"></i>Product Approvals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="withdrawals.php">
                            <i class="fas fa-money-bill-wave me-1"></i>Withdrawals
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-box me-2"></i>Product Management</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                        <i class="fas fa-plus me-1"></i>Add New Product
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-box"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_products']); ?></h4>
                            <small class="text-muted">Total Products</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['active_products']); ?></h4>
                            <small class="text-muted">Active Products</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">₹<?php echo number_format($stats['avg_price'], 2); ?></h4>
                            <small class="text-muted">Average Price</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['avg_pv'], 2); ?></h4>
                            <small class="text-muted">Average PV</small>
                        </div>
                    </div>
                </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by code, name, or description...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Products</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($products)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Product Code</th>
                                            <th>Name</th>
                                            <th>Price</th>
                                            <th>PV Value</th>
                                            <th>Status</th>
                                            <th>Usage</th>
                                            <th>Created By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $product): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($product['product_code']); ?></strong></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                    <?php if ($product['description']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?><?php echo strlen($product['description']) > 50 ? '...' : ''; ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><strong>₹<?php echo number_format($product['price'], 2); ?></strong></td>
                                                <td><span class="badge bg-primary"><?php echo formatPV($product['pv_value']); ?></span></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ucfirst($product['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($product['usage_count'] > 0): ?>
                                                        <span class="badge bg-info"><?php echo $product['usage_count']; ?> transactions</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Not used</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($product['created_by_name'] ?? 'System'); ?><br>
                                                    <small class="text-muted"><?php echo date('M d, Y', strtotime($product['created_at'])); ?></small>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <?php if ($product['usage_count'] == 0): ?>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo htmlspecialchars($product['name']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No products found</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="add_product">

                    <div class="modal-header">
                        <h5 class="modal-title">Add New Product</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="product_code" class="form-label">Product Code *</label>
                                    <input type="text" class="form-control" id="product_code" name="product_code" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Product Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price (₹) *</label>
                                    <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="pv_value" class="form-label">PV Value *</label>
                                    <input type="number" class="form-control" id="pv_value" name="pv_value" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="edit_product">
                    <input type="hidden" id="edit_product_id" name="product_id">

                    <div class="modal-header">
                        <h5 class="modal-title">Edit Product</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_product_code" class="form-label">Product Code *</label>
                                    <input type="text" class="form-control" id="edit_product_code" name="product_code" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_name" class="form-label">Product Name *</label>
                                    <input type="text" class="form-control" id="edit_name" name="name" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_price" class="form-label">Price (₹) *</label>
                                    <input type="number" class="form-control" id="edit_price" name="price" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_pv_value" class="form-label">PV Value *</label>
                                    <input type="number" class="form-control" id="edit_pv_value" name="pv_value" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="edit_status" class="form-label">Status</label>
                                    <select class="form-select" id="edit_status" name="status">
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Product Modal -->
    <div class="modal fade" id="deleteProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="delete_product">
                    <input type="hidden" id="delete_product_id" name="product_id">

                    <div class="modal-header">
                        <h5 class="modal-title">Delete Product</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Are you sure you want to delete this product?</strong>
                            <p class="mb-0 mt-2" id="deleteProductName"></p>
                            <p class="mb-0 mt-2"><small>This action cannot be undone.</small></p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editProduct(product) {
            document.getElementById('edit_product_id').value = product.id;
            document.getElementById('edit_product_code').value = product.product_code;
            document.getElementById('edit_name').value = product.name;
            document.getElementById('edit_description').value = product.description || '';
            document.getElementById('edit_price').value = product.price;
            document.getElementById('edit_pv_value').value = product.pv_value;
            document.getElementById('edit_status').value = product.status;

            new bootstrap.Modal(document.getElementById('editProductModal')).show();
        }

        function deleteProduct(productId, productName) {
            document.getElementById('delete_product_id').value = productId;
            document.getElementById('deleteProductName').textContent = 'Product: ' + productName;

            new bootstrap.Modal(document.getElementById('deleteProductModal')).show();
        }
    </script>
</body>
</html>
            </div>
        </div>
