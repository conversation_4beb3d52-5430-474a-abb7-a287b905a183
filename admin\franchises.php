<?php
/**
 * Admin Franchise Management Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

$db = Database::getInstance();

// Handle franchise actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'toggle_status') {
            $franchiseId = (int) $_POST['franchise_id'];
            $newStatus = $_POST['new_status'];
            
            if (!in_array($newStatus, ['active', 'inactive'])) {
                throw new Exception("Invalid status");
            }
            
            $stmt = $db->prepare("UPDATE franchise SET status = ? WHERE id = ?");
            $stmt->execute([$newStatus, $franchiseId]);
            
            $success = "Franchise status updated successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query conditions
$whereConditions = [];
$params = [];

if ($status && $status !== 'all') {
    $whereConditions[] = "f.status = ?";
    $params[] = $status;
}

if ($search) {
    $whereConditions[] = "(f.franchise_code LIKE ? OR f.full_name LIKE ? OR f.email LIKE ? OR f.phone LIKE ?)";
    $searchTerm = '%' . $search . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get franchises with statistics
$franchisesStmt = $db->prepare("
    SELECT f.*, 
           a.full_name as created_by_name,
           (SELECT COUNT(*) FROM users WHERE franchise_id = f.id) as total_users,
           (SELECT COUNT(*) FROM users WHERE franchise_id = f.id AND status = 'active') as active_users,
           (SELECT COUNT(*) FROM product_assignment_requests WHERE franchise_id = f.id) as total_requests,
           (SELECT COUNT(*) FROM product_assignment_requests WHERE franchise_id = f.id AND status = 'pending') as pending_requests,
           (SELECT COUNT(*) FROM product_assignment_requests WHERE franchise_id = f.id AND status = 'approved') as approved_requests
    FROM franchise f
    LEFT JOIN admin a ON f.created_by = a.id
    {$whereClause}
    ORDER BY f.created_at DESC
");
$franchisesStmt->execute($params);
$franchises = $franchisesStmt->fetchAll();

// Get statistics
$statsStmt = $db->query("
    SELECT 
        COUNT(*) as total_franchises,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_franchises,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_franchises,
        AVG(commission_rate) as avg_commission_rate
    FROM franchise
");
$stats = $statsStmt->fetch();

// Get recent activity
$recentActivityStmt = $db->query("
    SELECT 'user_registration' as activity_type, u.full_name as user_name, f.full_name as franchise_name, u.registration_date as activity_date
    FROM users u 
    JOIN franchise f ON u.franchise_id = f.id 
    WHERE DATE(u.registration_date) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
    UNION ALL
    SELECT 'product_request' as activity_type, CONCAT(u.full_name, ' - ', p.name) as user_name, f.full_name as franchise_name, par.requested_at as activity_date
    FROM product_assignment_requests par
    JOIN users u ON par.user_id = u.user_id
    JOIN franchise f ON par.franchise_id = f.id
    JOIN products p ON par.product_id = p.id
    WHERE DATE(par.requested_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAYS)
    ORDER BY activity_date DESC
    LIMIT 10
");
$recentActivity = $recentActivityStmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Franchise Management - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-user-shield me-2"></i><?php echo SITE_NAME; ?> Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="franchises.php">
                            <i class="fas fa-store me-1"></i>Franchises
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-1"></i>Products
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="product-approvals.php">
                            <i class="fas fa-check-circle me-1"></i>Product Approvals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="withdrawals.php">
                            <i class="fas fa-money-bill-wave me-1"></i>Withdrawals
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2><i class="fas fa-store me-2"></i>Franchise Management</h2>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary me-3">
                            <i class="fas fa-store"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['total_franchises']); ?></h4>
                            <small class="text-muted">Total Franchises</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['active_franchises']); ?></h4>
                            <small class="text-muted">Active Franchises</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning me-3">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['inactive_franchises']); ?></h4>
                            <small class="text-muted">Inactive Franchises</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon info me-3">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div>
                            <h4 class="mb-0"><?php echo number_format($stats['avg_commission_rate'], 2); ?>%</h4>
                            <small class="text-muted">Avg Commission Rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search by code, name, email, or phone...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Franchises Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Franchises</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($franchises)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Franchise Code</th>
                                            <th>Name & Contact</th>
                                            <th>Users</th>
                                            <th>Product Requests</th>
                                            <th>Commission Rate</th>
                                            <th>Status</th>
                                            <th>Registration</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($franchises as $franchise): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($franchise['franchise_code']); ?></strong></td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($franchise['full_name']); ?></strong><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($franchise['email']); ?><br>
                                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($franchise['phone']); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $franchise['total_users']; ?> Total</span><br>
                                                    <span class="badge bg-success"><?php echo $franchise['active_users']; ?> Active</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $franchise['total_requests']; ?> Total</span><br>
                                                    <?php if ($franchise['pending_requests'] > 0): ?>
                                                        <span class="badge bg-warning"><?php echo $franchise['pending_requests']; ?> Pending</span><br>
                                                    <?php endif; ?>
                                                    <span class="badge bg-success"><?php echo $franchise['approved_requests']; ?> Approved</span>
                                                </td>
                                                <td><strong><?php echo number_format($franchise['commission_rate'], 2); ?>%</strong></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $franchise['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ucfirst($franchise['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo date('M d, Y', strtotime($franchise['created_at'])); ?><br>
                                                    <small class="text-muted">By: <?php echo htmlspecialchars($franchise['created_by_name'] ?? 'System'); ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($franchise['status'] === 'active'): ?>
                                                        <button class="btn btn-sm btn-warning" onclick="toggleStatus(<?php echo $franchise['id']; ?>, 'inactive', '<?php echo htmlspecialchars($franchise['full_name']); ?>')">
                                                            <i class="fas fa-pause"></i> Deactivate
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-success" onclick="toggleStatus(<?php echo $franchise['id']; ?>, 'active', '<?php echo htmlspecialchars($franchise['full_name']); ?>')">
                                                            <i class="fas fa-play"></i> Activate
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No franchises found</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Activity (Last 7 Days)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentActivity)): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recentActivity as $activity): ?>
                                    <div class="list-group-item d-flex align-items-center">
                                        <div class="me-3">
                                            <?php if ($activity['activity_type'] === 'user_registration'): ?>
                                                <i class="fas fa-user-plus fa-2x text-success"></i>
                                            <?php else: ?>
                                                <i class="fas fa-box fa-2x text-info"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                <?php if ($activity['activity_type'] === 'user_registration'): ?>
                                                    New User Registration
                                                <?php else: ?>
                                                    Product Assignment Request
                                                <?php endif; ?>
                                            </h6>
                                            <p class="mb-1"><?php echo htmlspecialchars($activity['user_name']); ?></p>
                                            <small class="text-muted">
                                                Franchise: <?php echo htmlspecialchars($activity['franchise_name']); ?> •
                                                <?php echo timeAgo($activity['activity_date']); ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No recent activity</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Toggle Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <?php echo csrfTokenInput(); ?>
                    <input type="hidden" name="action" value="toggle_status">
                    <input type="hidden" id="modal_franchise_id" name="franchise_id">
                    <input type="hidden" id="modal_new_status" name="new_status">

                    <div class="modal-header">
                        <h5 class="modal-title" id="modalTitle">Confirm Status Change</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="modalContent"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn" id="modalSubmitBtn">Confirm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleStatus(franchiseId, newStatus, franchiseName) {
            document.getElementById('modal_franchise_id').value = franchiseId;
            document.getElementById('modal_new_status').value = newStatus;

            const modal = document.getElementById('statusModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');
            const submitBtn = document.getElementById('modalSubmitBtn');

            if (newStatus === 'active') {
                title.textContent = 'Activate Franchise';
                content.innerHTML = `<div class="alert alert-success">
                    <i class="fas fa-play-circle me-2"></i>
                    <strong>Activate franchise "${franchiseName}"?</strong>
                    <p class="mb-0 mt-2">This will allow the franchise to operate and assign products to users.</p>
                </div>`;
                submitBtn.className = 'btn btn-success';
                submitBtn.innerHTML = '<i class="fas fa-play me-1"></i>Activate';
            } else {
                title.textContent = 'Deactivate Franchise';
                content.innerHTML = `<div class="alert alert-warning">
                    <i class="fas fa-pause-circle me-2"></i>
                    <strong>Deactivate franchise "${franchiseName}"?</strong>
                    <p class="mb-0 mt-2">This will prevent the franchise from operating and assigning products to users.</p>
                </div>`;
                submitBtn.className = 'btn btn-warning';
                submitBtn.innerHTML = '<i class="fas fa-pause me-1"></i>Deactivate';
            }

            new bootstrap.Modal(modal).show();
        }
    </script>
</body>
</html>
